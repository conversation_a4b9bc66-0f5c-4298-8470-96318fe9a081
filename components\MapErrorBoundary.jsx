import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ErrorBoundary } from 'react-error-boundary';

// Palette de couleurs CleanSpot
const COLORS = {
  background: '#F9FAFB',
  primary: '#1E3A8A',
  accent: '#10B981',
  text: '#111827',
  textSecondary: '#6B7280',
  white: '#FFFFFF',
  error: '#EF4444',
};

// Composant d'erreur fallback pour la carte
function MapErrorFallback({ error, resetErrorBoundary }) {
  console.error('🚨 Map Error Boundary triggered:', error);
  
  return (
    <View style={styles.errorContainer}>
      <Text style={styles.errorIcon}>🗺️💥</Text>
      <Text style={styles.errorTitle}>Erreur de carte</Text>
      <Text style={styles.errorMessage}>
        La carte a rencontré un problème technique. Cela peut être dû à une connexion instable ou à des interactions trop rapides.
      </Text>
      <Text style={styles.errorDetails}>
        Erreur: {error.message}
      </Text>
      <TouchableOpacity 
        style={styles.retryButton}
        onPress={resetErrorBoundary}
      >
        <Text style={styles.retryButtonText}>Réessayer</Text>
      </TouchableOpacity>
    </View>
  );
}

// Gestionnaire d'erreur personnalisé
const handleError = (error, errorInfo) => {
  console.error('🚨 Map Error Details:', {
    error: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    timestamp: new Date().toISOString(),
  });
  
  // En production, vous pourriez envoyer ces erreurs à un service de monitoring
  // comme Sentry, Bugsnag, ou Firebase Crashlytics
};

// Composant ErrorBoundary pour la carte
export default function MapErrorBoundary({ children, onReset }) {
  return (
    <ErrorBoundary
      FallbackComponent={MapErrorFallback}
      onError={handleError}
      onReset={onReset}
      resetKeys={['mapRegion']} // Reset quand la région change
    >
      {children}
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.error,
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  errorDetails: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontFamily: 'monospace',
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  retryButton: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
