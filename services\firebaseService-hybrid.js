// Hybrid Firebase Service - Works with both real Firebase and mock mode

// Import mock services
import { 
  authService as mockAuthService,
  storageService as mockStorageService,
  firestoreService as mockFirestoreService
} from './firebaseService-simple';

// Import real services
import { 
  authService as realAuthService,
  storageService as realStorageService,
  firestoreService as realFirestoreService
} from './firebaseService';

// Configuration flag - set to false for mock mode, true for real Firebase
let isMockMode = false;

// Try to use real Firebase, fall back to mock if there's an error
try {
  // Test if Firebase is properly initialized
  const testAuth = realAuthService.getCurrentUser();
  console.log("🔥 Using REAL Firebase services");
} catch (error) {
  console.warn("⚠️ Real Firebase not available, falling back to mock mode");
  isMockMode = true;
}

// Export the appropriate services based on mode
export const authService = isMockMode ? mockAuthService : realAuthService;
export const storageService = isMockMode ? mockStorageService : realStorageService;
export const firestoreService = isMockMode ? mockFirestoreService : realFirestoreService;

// Export configuration for debugging
export const getFirebaseMode = () => isMockMode ? 'MOCK' : 'REAL';

console.log(`🔧 Firebase Service Hybrid loaded - Mode: ${isMockMode ? 'MOCK' : 'REAL'}`);